import 'dart:io';

import 'package:toii_mesh/core/service/user_service.dart';
import 'package:toii_mesh/model/base/base_response.dart';
import 'package:toii_mesh/model/media/media_model.dart';
import 'package:toii_mesh/model/user/update_user_request_model.dart';
import 'package:toii_mesh/model/user/user_model.dart';

abstract class UserRepository {
  Future<BaseResponse<UserModel>> getUserStats(String userId);
  Future<BaseResponse<MediaUploadResponseModel>> uploadMedia(
    File file,
    String type,
  );
    Future<BaseResponse<UserModel>> updateUser(
    String userId,
    UpdateUserRequestModel request,
  );
}

class UserRepositoryImpl extends UserRepository {
  final UserService userService;

  UserRepositoryImpl({required this.userService});

  @override
  Future<BaseResponse<UserModel>> getUserStats(String userId) async {
    try {
      return await userService.getUserStats(userId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<MediaUploadResponseModel>> uploadMedia(
    File file,
    String type,
  ) async {
    return await userService.uploadMedia(file, type);
  }
  
  @override
  Future<BaseResponse<UserModel>> updateUser(String userId, UpdateUserRequestModel request) async {
   try {
      return await userService.updateUser(userId, request);
    } catch (e) {
      rethrow;
    }
  }
}
