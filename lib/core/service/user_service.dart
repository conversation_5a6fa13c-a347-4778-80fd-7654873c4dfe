import 'dart:io';

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:toii_mesh/model/base/base_response.dart';
import 'package:toii_mesh/model/media/media_model.dart';
import 'package:toii_mesh/model/user/update_user_request_model.dart';
import 'package:toii_mesh/model/user/user_model.dart';

part 'user_service.g.dart';

@RestApi()
abstract class UserService {
  factory UserService(Dio dio, {String baseUrl}) = _UserService;

  @GET('/user/api/v1/users/{user_id}/profile')
  Future<BaseResponse<UserModel>> getUserStats(@Path('user_id') String userId);

  @MultiPart()
  @POST('/social/api/v1/media')
  Future<BaseResponse<MediaUploadResponseModel>> uploadMedia(
    @Part(name: 'file') File file,
    @Part(name: 'type') String type,
  );

    @PUT('/user/api/v1/users/{user_id}')
  Future<BaseResponse<UserModel>> updateUser(
    @Path('user_id') String userId,
    @Body() UpdateUserRequestModel request,
  );
}
