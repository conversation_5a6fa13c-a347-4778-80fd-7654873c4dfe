import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';
import 'package:toii_mesh/core/repository/user_repository.dart';
import 'package:toii_mesh/model/base/base_response.dart';
import 'package:toii_mesh/model/media/media_model.dart';

part 'avatar_upload_state.dart';

class AvatarUploadCubit extends Cubit<AvatarUploadState> {
  AvatarUploadCubit() : super(const AvatarUploadState());

  final ImagePicker _imagePicker = ImagePicker();
  final UserRepository _socialRepository = GetIt.instance<UserRepository>();

  /// Pick image from gallery
  Future<void> pickImageFromGallery() async {
    try {
      emit(state.copyWith(status: AvatarUploadStatus.loading));

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        // Don't emit success yet, just update the local image path
        emit(state.copyWith(localImagePath: image.path, errorMessage: null));

        // Automatically upload the selected image
        await uploadAvatar(image);
      } else {
        emit(
          state.copyWith(
            status: AvatarUploadStatus.initial,
            errorMessage: null,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: AvatarUploadStatus.failure,
          errorMessage: 'Error selecting image: ${e.toString()}',
        ),
      );
    }
  }

  /// Pick image from camera
  Future<void> pickImageFromCamera() async {
    try {
      emit(state.copyWith(status: AvatarUploadStatus.loading));

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        // Don't emit success yet, just update the local image path
        emit(state.copyWith(localImagePath: image.path, errorMessage: null));

        // Automatically upload the selected image
        await uploadAvatar(image);
      } else {
        emit(
          state.copyWith(
            status: AvatarUploadStatus.initial,
            errorMessage: null,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: AvatarUploadStatus.failure,
          errorMessage: 'Error taking photo: ${e.toString()}',
        ),
      );
    }
  }

  /// Upload avatar to server using platform-appropriate method
  Future<void> uploadAvatar(XFile image) async {
    try {
      emit(
        state.copyWith(status: AvatarUploadStatus.loading, uploadProgress: 0.0),
      );

      late BaseResponse<MediaUploadResponseModel> result;

      // For mobile, use file path
      final file = File(image.path);
      result = await _socialRepository.uploadMedia(file, 'image');

      emit(
        state.copyWith(
          status: AvatarUploadStatus.success,
          mediaKey: result.data.key,
          uploadedImageUrl: state.localImagePath!,
          uploadProgress: 1.0,
          errorMessage: null,
        ),
      );
    } catch (e) {
      final errorMessage = 'Failed to upload avatar: ${e.toString()}';

      emit(
        state.copyWith(
          status: AvatarUploadStatus.failure,
          errorMessage: errorMessage,
          uploadProgress: 0.0,
        ),
      );
    }
  }

  /// Clear the current avatar selection
  void clearAvatar() {
    emit(const AvatarUploadState());
  }

  /// Reset state to initial
  void resetState() {
    emit(const AvatarUploadState());
  }
}
