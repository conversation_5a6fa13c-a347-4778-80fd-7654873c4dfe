import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel {
  final String id;
  final String username;
  final String? phoneNumber;
  final String? email;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'first_name')
  final String? firstName;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_name')
  final String? lastName;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'full_name')
  final String? fullName;

  final String? avatar;
  final String? address;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'wallet_address')
  final String? walletAddress;

  final String? bio;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_activity')
  final DateTime? lastActivity;

  final String? role;
  final String? status;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime? createdAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime? updatedAt;

  @J<PERSON><PERSON><PERSON>(name: 'deleted_at')
  final DateTime? deletedAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'followers_count')
  final int? followersCount;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'following_count')
  final int? followingCount;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'posts_count')
  final int? postsCount;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_followed')
  final bool? isFollowed;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'avatar_url')
  final String? avatarUrl;

  @Json<PERSON>ey(name: 'background_url')
  final String? backgroundUrl;

  @JsonKey(name: 'is_verified', defaultValue: false)
  final bool? isVerified;

  UserModel({
    required this.id,
    required this.username,
    this.phoneNumber,
    this.email,
    this.firstName,
    this.lastName,
    this.fullName,
    this.avatar,
    this.address,
    this.bio,
    this.lastActivity,
    this.role,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.walletAddress,
    this.followersCount,
    this.followingCount,
    this.postsCount,
    this.isFollowed,
    this.avatarUrl,
    this.backgroundUrl,
    this.isVerified,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);
}

class ProfilesModel {
  final List<UserModel> profiles;

  ProfilesModel({required this.profiles});

  factory ProfilesModel.fromJson(Map<String, dynamic> json) {
    final profilesJson = json['profiles'] as List<dynamic>;
    final profiles = profilesJson.map((e) => UserModel.fromJson(e)).toList();
    return ProfilesModel(profiles: profiles);
  }
}

extension UserModelExtension on UserModel {
  String get displayName =>
      (fullName ?? '').isNotEmpty ? fullName ?? "" : username;
}
