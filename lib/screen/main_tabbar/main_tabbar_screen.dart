import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_mesh/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_cubit.dart';
import 'package:toii_mesh/screen/chats/chats_tab_screen.dart';
import 'package:toii_mesh/screen/tabs/contacts_tab_screen.dart';
import 'package:toii_mesh/screen/tabs/people_tab_screen.dart';
import 'package:toii_mesh/screen/user_setting/settings_tab_screen.dart';
import 'package:toii_mesh/widget/navigation/bottom_navigation_bar.dart';

class MainTabbarScreen extends StatefulWidget {
  const MainTabbarScreen({super.key});

  @override
  State<MainTabbarScreen> createState() => _MainTabbarScreenState();
}

class _MainTabbarScreenState extends State<MainTabbarScreen>
    with SingleTickerProviderStateMixin {
  int _currentIndex = 0;
  late PageController _pageController;

  final List<Widget> _screens = [
    const ChatsTabScreen(),
    const ContactsTabScreen(),
    const PeopleTabScreen(),
    const SettingsTabScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (_) => GetIt.instance<ProfileCubit>()..getProfile(),
        ),
        BlocProvider.value(
          value: GetIt.instance<XmtpCubit>(),
        ),
      ],
      child: BlocBuilder<ProfileCubit, ProfileState>(
        bloc: GetIt.instance<ProfileCubit>(),
        builder: (context, state) {
          return Scaffold(
            backgroundColor: Colors.white,
            body: Column(
              children: [
                // Status bar
                Container(
                  height: MediaQuery.of(context).padding.top,
                  color: Colors.white,
                ),

                // Main content
                Expanded(
                  child: PageView(
                    controller: _pageController,
                    onPageChanged: (index) {
                      setState(() {
                        _currentIndex = index;
                      });
                    },
                    children: _screens,
                  ),
                ),
              ],
            ),
            // Bottom navigation
            bottomNavigationBar: CustomBottomNavigationBar(
              currentIndex: _currentIndex,
              onTap: _onTabTapped,
            ),
          );
        },
      ),
    );
  }
}
