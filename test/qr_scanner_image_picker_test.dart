import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_cubit.dart';
import 'package:toii_mesh/screen/chats/qr_scanner_screen.dart';

// Mock XmtpCubit for testing
class MockXmtpCubit extends XmtpCubit {
  MockXmtpCubit() : super();
  
  @override
  bool get hasClient => true;
  
  @override
  String? get inboxId => 'test_inbox_id';
  
  @override
  Future<void> createDm(String inboxId) async {
    // Mock implementation
  }
}

void main() {
  group('QR Scanner Image Picker Tests', () {
    testWidgets('QR Scanner should have gallery button in app bar', (WidgetTester tester) async {
      // Create a mock XmtpCubit
      final mockXmtpCubit = MockXmtpCubit();
      
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<XmtpCubit>.value(
            value: mockXmtpCubit,
            child: const QrScannerScreen(),
          ),
        ),
      );
      
      // Verify that the gallery button is present
      expect(find.byIcon(Icons.photo_library), findsOneWidget);
      
      // Verify that the flash button is still present
      expect(find.byIcon(Icons.flash_on), findsOneWidget);
      
      // Verify that the close button is present
      expect(find.byIcon(Icons.close), findsOneWidget);
    });
    
    testWidgets('Gallery button should have correct tooltip', (WidgetTester tester) async {
      final mockXmtpCubit = MockXmtpCubit();
      
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<XmtpCubit>.value(
            value: mockXmtpCubit,
            child: const QrScannerScreen(),
          ),
        ),
      );
      
      // Find the gallery button
      final galleryButton = find.byIcon(Icons.photo_library);
      expect(galleryButton, findsOneWidget);
      
      // Check if the button has the correct tooltip
      final iconButton = tester.widget<IconButton>(
        find.ancestor(
          of: galleryButton,
          matching: find.byType(IconButton),
        ).first,
      );
      
      expect(iconButton.tooltip, equals('Select from Gallery'));
    });
    
    testWidgets('App bar should have correct title', (WidgetTester tester) async {
      final mockXmtpCubit = MockXmtpCubit();
      
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<XmtpCubit>.value(
            value: mockXmtpCubit,
            child: const QrScannerScreen(),
          ),
        ),
      );
      
      // Verify the app bar title
      expect(find.text('Scan QR Code'), findsOneWidget);
    });
  });
}
