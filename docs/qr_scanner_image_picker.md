# QR Scanner với Image Picker

## Tổng quan

QR Scanner Screen đã được cập nhật để hỗ trợ scan QR code từ ảnh trong thư viện thiết bị, ngo<PERSON>i việc scan trực tiếp bằng camera.

## Tính năng mới

### 1. Button Gallery trong App Bar
- **Icon**: `Icons.photo_library`
- **Vị trí**: App bar, bên trái button flash
- **Tooltip**: "Select from Gallery"
- **Chức năng**: Mở gallery để chọn ảnh chứa QR code

### 2. Chọn ảnh từ Gallery
- Sử dụng thư viện `image_picker`
- Chất lượng ảnh: 100% (để đảm bảo QR code rõ nét)
- Hỗ trợ các format ảnh phổ biến (JPG, PNG, etc.)

### 3. Phân tích QR Code từ ảnh
- Sử dụng `google_mlkit_barcode_scanning` thay vì `mobile_scanner.analyzeImage`
- Chỉ scan QR code (không scan barcode khác)
- Tự động đóng scanner sau khi xử lý

## Thư viện sử dụng

### Dependencies đã thêm:
```yaml
dependencies:
  image_picker: # Đã có sẵn
  google_mlkit_barcode_scanning: ^0.14.1 # Mới thêm
```

### Import statements:
```dart
import 'package:google_mlkit_barcode_scanning/google_mlkit_barcode_scanning.dart' as mlkit;
import 'package:image_picker/image_picker.dart';
```

## Cách sử dụng

### Cho người dùng:
1. Mở QR Scanner Screen
2. Tap vào icon thư viện ảnh (📷) trong app bar
3. Chọn ảnh chứa QR code từ gallery
4. App sẽ tự động phân tích và xử lý QR code

### Cho developer:

#### Method chính:
```dart
Future<void> _pickImageFromGallery() async {
  // Chọn ảnh từ gallery
  final XFile? image = await _imagePicker.pickImage(
    source: ImageSource.gallery,
    imageQuality: 100,
  );
  
  if (image != null) {
    await _analyzeImageForQrCode(image.path);
  }
}

Future<void> _analyzeImageForQrCode(String imagePath) async {
  // Tạo InputImage từ file path
  final inputImage = mlkit.InputImage.fromFilePath(imagePath);
  
  // Tạo barcode scanner chỉ cho QR code
  final barcodeScanner = mlkit.BarcodeScanner(
    formats: [mlkit.BarcodeFormat.qrCode],
  );

  // Scan QR codes
  final List<mlkit.Barcode> barcodes = await barcodeScanner.processImage(inputImage);
  
  // Xử lý kết quả
  if (barcodes.isNotEmpty) {
    for (final barcode in barcodes) {
      final String? code = barcode.rawValue;
      if (code != null && code.isNotEmpty) {
        await _processQrCode(code); // Sử dụng logic xử lý hiện có
        break;
      }
    }
  }
  
  // Đóng scanner
  await barcodeScanner.close();
}
```

## Error Handling

### Các lỗi có thể xảy ra:
1. **"Failed to pick image"**: Lỗi khi chọn ảnh từ gallery
2. **"No QR code found in the selected image"**: Ảnh không chứa QR code
3. **"Failed to analyze image"**: Lỗi khi phân tích ảnh

### Xử lý lỗi:
- Hiển thị SnackBar với thông báo lỗi
- Reset trạng thái `_isProcessing`
- Log chi tiết lỗi với `debugPrint`

## Testing

### Unit Tests:
- Test button gallery xuất hiện trong app bar
- Test tooltip của button
- Test title của app bar

### Manual Testing:
1. Test chọn ảnh từ gallery
2. Test scan QR code từ ảnh
3. Test xử lý kết quả connect XMTP
4. Test error handling

## Lưu ý kỹ thuật

### Tại sao sử dụng google_mlkit_barcode_scanning?
- `mobile_scanner.analyzeImage` có vấn đề trên iOS và một số Android devices
- `google_mlkit_barcode_scanning` ổn định hơn cho việc phân tích ảnh tĩnh
- Hỗ trợ tốt hơn cho việc xử lý file path

### Performance:
- Scanner được đóng sau mỗi lần sử dụng để giải phóng memory
- Chỉ scan QR code để tăng tốc độ xử lý
- Sử dụng imageQuality: 100 để đảm bảo chất lượng scan

### Compatibility:
- Android: ✅ Hoạt động tốt
- iOS: ✅ Hoạt động tốt  
- Web: ⚠️ Cần test thêm

## Troubleshooting

### Nếu không scan được QR code từ ảnh:
1. Kiểm tra ảnh có rõ nét không
2. Kiểm tra QR code có bị méo hoặc bị che không
3. Thử với ảnh QR code khác
4. Kiểm tra log để xem có lỗi gì không

### Nếu app crash:
1. Kiểm tra permissions cho gallery access
2. Kiểm tra file path có hợp lệ không
3. Kiểm tra memory usage
